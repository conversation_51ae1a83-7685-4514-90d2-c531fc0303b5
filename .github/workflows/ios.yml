name: Build for iOS

on:
  pull_request:
        types:
          - opened
          - synchronize
          - reopened
          - ready_for_review
        paths-ignore:
          - '**.md'
  workflow_dispatch:
    inputs:
      branch:
        required: false
        default: 'main'

jobs:
  build-macos-app:
    name: Release IOS
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}
          fetch-depth: 0

      - name: Setup flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version-file: pubspec.yaml

      - name: 更新版本号
        run: |
          version_name=$(yq e '.version' pubspec.yaml | cut -d "+" -f 1)
          sed -i '' "s/version: .*/version: $version_name+$(git rev-list --count HEAD)/" pubspec.yaml

      - name: Build iOS
        run: |
          chmod +x lib/scripts/build.sh
          lib/scripts/build.sh
          flutter build ios --release --no-codesign 
          ln -sf ./build/ios/iphoneos Payload
          zip -r9 ios-release-no-sign.ipa Payload/runner.app

      - name: Upload ios release
        uses: actions/upload-artifact@v4
        with:
          name: ios-release
          path: ios-release-no-sign.ipa
