PODS:
  - appscheme (1.0.4):
    - Flutter
  - audio_service (0.0.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - auto_orientation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_mailer (0.0.1):
    - Flutter
  - flutter_volume_controller (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - gt3_flutter_plugin (0.0.8):
    - Flutter
    - GT3Captcha-iOS
  - GT3Captcha-iOS (0.15.8.3)
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_native_event_loop (1.0.0):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - ReachabilitySwift (5.0.0)
  - saver_gallery (0.0.1):
    - Flutter
  - screen_brightness_ios (0.1.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - status_bar_control (3.2.1):
    - Flutter
  - system_proxy (0.0.1):
    - Flutter
  - Toast (4.1.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_cookie_manager (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - appscheme (from `.symlinks/plugins/appscheme/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - auto_orientation (from `.symlinks/plugins/auto_orientation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_mailer (from `.symlinks/plugins/flutter_mailer/ios`)
  - flutter_volume_controller (from `.symlinks/plugins/flutter_volume_controller/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - gt3_flutter_plugin (from `.symlinks/plugins/gt3_flutter_plugin/ios`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_native_event_loop (from `.symlinks/plugins/media_kit_native_event_loop/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - saver_gallery (from `.symlinks/plugins/saver_gallery/ios`)
  - screen_brightness_ios (from `.symlinks/plugins/screen_brightness_ios/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - status_bar_control (from `.symlinks/plugins/status_bar_control/ios`)
  - system_proxy (from `.symlinks/plugins/system_proxy/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_cookie_manager (from `.symlinks/plugins/webview_cookie_manager/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FMDB
    - GT3Captcha-iOS
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  appscheme:
    :path: ".symlinks/plugins/appscheme/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  auto_orientation:
    :path: ".symlinks/plugins/auto_orientation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_mailer:
    :path: ".symlinks/plugins/flutter_mailer/ios"
  flutter_volume_controller:
    :path: ".symlinks/plugins/flutter_volume_controller/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  gt3_flutter_plugin:
    :path: ".symlinks/plugins/gt3_flutter_plugin/ios"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_native_event_loop:
    :path: ".symlinks/plugins/media_kit_native_event_loop/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  saver_gallery:
    :path: ".symlinks/plugins/saver_gallery/ios"
  screen_brightness_ios:
    :path: ".symlinks/plugins/screen_brightness_ios/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  status_bar_control:
    :path: ".symlinks/plugins/status_bar_control/ios"
  system_proxy:
    :path: ".symlinks/plugins/system_proxy/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_cookie_manager:
    :path: ".symlinks/plugins/webview_cookie_manager/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  appscheme: b1c3f8862331cb20430cf9e0e4af85dbc1572ad8
  audio_service: f509d65da41b9521a61f1c404dd58651f265a567
  audio_session: 4f3e461722055d21515cf3261b64c973c062f345
  auto_orientation: 102ed811a5938d52c86520ddd7ecd3a126b5d39d
  connectivity_plus: 07c49e96d7fc92bc9920617b83238c4d178b446a
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_mailer: 2ef5a67087bc8c6c4cefd04a178bf1ae2c94cd83
  flutter_volume_controller: e4d5832f08008180f76e30faf671ffd5a425e529
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  gt3_flutter_plugin: bfa1f26e9a09dc00401514be5ed437f964cabf23
  GT3Captcha-iOS: 5e3b1077834d8a9d6f4d64a447a30af3e14affe6
  media_kit_libs_ios_video: a5fe24bc7875ccd6378a0978c13185e1344651c1
  media_kit_native_event_loop: e6b2ab20cf0746eb1c33be961fcf79667304fa2a
  media_kit_video: 5da63f157170e5bf303bf85453b7ef6971218a2e
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  saver_gallery: 2b4e584106fde2407ab51560f3851564963e6b78
  screen_brightness_ios: 715ca807df953bf676d339f11464e438143ee625
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  status_bar_control: 7c84146799e6a076315cc1550f78ef53aae3e446
  system_proxy: bec1a5c5af67dd3e3ebf43979400a8756c04cc44
  Toast: ec33c32b8688982cecc6348adeae667c1b9938da
  url_launcher_ios: bf5ce03e0e2088bad9cc378ea97fa0ed5b49673b
  volume_controller: 531ddf792994285c9b17f9d8a7e4dcdd29b3eae9
  wakelock_plus: 8b09852c8876491e4b6d179e17dfe2a0b5f60d47
  webview_cookie_manager: eaf920722b493bd0f7611b5484771ca53fed03f7
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a

PODFILE CHECKSUM: 637cd290bed23275b5f5ffcc7eb1e73d0a5fb2be

COCOAPODS: 1.14.3
