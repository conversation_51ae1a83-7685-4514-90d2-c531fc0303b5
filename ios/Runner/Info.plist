<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>PiliPlus</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>PiliPlus</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>请允许APP保存图片到相册</string>
		<key>NSCameraUsageDescription</key>
		<string>App需要您的同意,才能访问相册</string>
		<key>NSAppleMusicUsageDescription</key>
		<string>App需要您的同意,才能访问媒体资料库</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>https</string>
			<string>http</string>
		</array>
		<!-- Add Scheme related information -->
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string></string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>http</string>
					<string>https</string>
				</array>
				<key>CFBundleURLTypes</key>
				<array>
					<dict>
						<key>CFBundleURLName</key>
						<string></string>
						<key>CFBundleURLSchemes</key>
						<array>
							<string>m.bilibili.com</string>
							<string>bilibili.com</string>
							<string>www.bilibili.com</string>
							<string>bangumi.bilibili.com</string>
							<string>bilibili.cn</string>
							<string>www.bilibili.cn</string>
							<string>bangumi.bilibili.cn</string>
							<string>bilibili.tv</string>
							<string>www.bilibili.tv</string>
							<string>bangumi.bilibili.tv</string>
							<string>miniapp.bilibili.com</string>
							<string>live.bilibili.com</string>
						</array>
					</dict>
				</array>
			</dict>
			<!-- 当其他应用程序或系统通过 bilibili -->
			<dict>
				<key>CFBundleURLName</key>
				<string>bilibili</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>bilibili</string>
				</array>
			</dict>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
		</array>
		<key>UIStatusBarHidden</key>
		<false/>
	</dict>
</plist>
