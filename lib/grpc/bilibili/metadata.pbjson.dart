//
//  Generated code. Do not modify.
//  source: bilibili/metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use metadataDescriptor instead')
const Metadata$json = {
  '1': 'Metadata',
  '2': [
    {'1': 'access_key', '3': 1, '4': 1, '5': 9, '10': 'accessKey'},
    {'1': 'mobi_app', '3': 2, '4': 1, '5': 9, '10': 'mobiApp'},
    {'1': 'device', '3': 3, '4': 1, '5': 9, '10': 'device'},
    {'1': 'build', '3': 4, '4': 1, '5': 5, '10': 'build'},
    {'1': 'channel', '3': 5, '4': 1, '5': 9, '10': 'channel'},
    {'1': 'buvid', '3': 6, '4': 1, '5': 9, '10': 'buvid'},
    {'1': 'platform', '3': 7, '4': 1, '5': 9, '10': 'platform'},
  ],
};

/// Descriptor for `Metadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List metadataDescriptor = $convert.base64Decode(
    'CghNZXRhZGF0YRIdCgphY2Nlc3Nfa2V5GAEgASgJUglhY2Nlc3NLZXkSGQoIbW9iaV9hcHAYAi'
    'ABKAlSB21vYmlBcHASFgoGZGV2aWNlGAMgASgJUgZkZXZpY2USFAoFYnVpbGQYBCABKAVSBWJ1'
    'aWxkEhgKB2NoYW5uZWwYBSABKAlSB2NoYW5uZWwSFAoFYnV2aWQYBiABKAlSBWJ1dmlkEhoKCH'
    'BsYXRmb3JtGAcgASgJUghwbGF0Zm9ybQ==');

