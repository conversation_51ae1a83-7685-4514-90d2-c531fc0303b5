//
//  Generated code. Do not modify.
//  source: bilibili/metadata/device.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use deviceDescriptor instead')
const Device$json = {
  '1': 'Device',
  '2': [
    {'1': 'app_id', '3': 1, '4': 1, '5': 5, '10': 'appId'},
    {'1': 'build', '3': 2, '4': 1, '5': 5, '10': 'build'},
    {'1': 'buvid', '3': 3, '4': 1, '5': 9, '10': 'buvid'},
    {'1': 'mobi_app', '3': 4, '4': 1, '5': 9, '10': 'mobiApp'},
    {'1': 'platform', '3': 5, '4': 1, '5': 9, '10': 'platform'},
    {'1': 'device', '3': 6, '4': 1, '5': 9, '10': 'device'},
    {'1': 'channel', '3': 7, '4': 1, '5': 9, '10': 'channel'},
    {'1': 'brand', '3': 8, '4': 1, '5': 9, '10': 'brand'},
    {'1': 'model', '3': 9, '4': 1, '5': 9, '10': 'model'},
    {'1': 'osver', '3': 10, '4': 1, '5': 9, '10': 'osver'},
    {'1': 'fp_local', '3': 11, '4': 1, '5': 9, '10': 'fpLocal'},
    {'1': 'fp_remote', '3': 12, '4': 1, '5': 9, '10': 'fpRemote'},
    {'1': 'version_name', '3': 13, '4': 1, '5': 9, '10': 'versionName'},
    {'1': 'fp', '3': 14, '4': 1, '5': 9, '10': 'fp'},
    {'1': 'fts', '3': 15, '4': 1, '5': 3, '10': 'fts'},
    {'1': 'guest_id', '3': 16, '4': 1, '5': 9, '10': 'guestId'},
  ],
};

/// Descriptor for `Device`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deviceDescriptor = $convert.base64Decode(
    'CgZEZXZpY2USFQoGYXBwX2lkGAEgASgFUgVhcHBJZBIUCgVidWlsZBgCIAEoBVIFYnVpbGQSFA'
    'oFYnV2aWQYAyABKAlSBWJ1dmlkEhkKCG1vYmlfYXBwGAQgASgJUgdtb2JpQXBwEhoKCHBsYXRm'
    'b3JtGAUgASgJUghwbGF0Zm9ybRIWCgZkZXZpY2UYBiABKAlSBmRldmljZRIYCgdjaGFubmVsGA'
    'cgASgJUgdjaGFubmVsEhQKBWJyYW5kGAggASgJUgVicmFuZBIUCgVtb2RlbBgJIAEoCVIFbW9k'
    'ZWwSFAoFb3N2ZXIYCiABKAlSBW9zdmVyEhkKCGZwX2xvY2FsGAsgASgJUgdmcExvY2FsEhsKCW'
    'ZwX3JlbW90ZRgMIAEoCVIIZnBSZW1vdGUSIQoMdmVyc2lvbl9uYW1lGA0gASgJUgt2ZXJzaW9u'
    'TmFtZRIOCgJmcBgOIAEoCVICZnASEAoDZnRzGA8gASgDUgNmdHMSGQoIZ3Vlc3RfaWQYECABKA'
    'lSB2d1ZXN0SWQ=');

@$core.Deprecated('Use deviceTypeDescriptor instead')
const DeviceType$json = {
  '1': 'DeviceType',
};

/// Descriptor for `DeviceType`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deviceTypeDescriptor = $convert.base64Decode(
    'CgpEZXZpY2VUeXBl');

@$core.Deprecated('Use mobiAppDescriptor instead')
const MobiApp$json = {
  '1': 'MobiApp',
};

/// Descriptor for `MobiApp`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List mobiAppDescriptor = $convert.base64Decode(
    'CgdNb2JpQXBw');

@$core.Deprecated('Use platformDescriptor instead')
const Platform$json = {
  '1': 'Platform',
};

/// Descriptor for `Platform`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List platformDescriptor = $convert.base64Decode(
    'CghQbGF0Zm9ybQ==');

