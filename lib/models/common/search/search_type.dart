// ignore_for_file: constant_identifier_names
enum SearchType {
  // all('综合'),
  // 视频：video
  video('视频', hasHeader: true),
  // 番剧：media_bangumi,
  media_bangumi('番剧'),
  // 影视：media_ft
  media_ft('影视'),
  // 直播间及主播：live
  // live,
  // 直播间：live_room
  live_room('直播间'),
  // 主播：live_user
  // live_user,
  // 话题：topic
  // topic,
  // 用户：bili_user
  bili_user('用户', hasHeader: true),
  // 专栏：article
  article('专栏', hasHeader: true);
  // 相簿：photo
  // photo

  final bool hasHeader;
  final String label;
  const SearchType(this.label, {this.hasHeader = false});
}
