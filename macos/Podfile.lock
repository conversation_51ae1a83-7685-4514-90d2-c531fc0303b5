PODS:
  - app_links (6.4.1):
    - FlutterMacOS
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - dynamic_color (0.0.2):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - flutter_volume_controller (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_native_event_loop (1.0.0):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - audio_service (from `Flutter/ephemeral/.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - dynamic_color (from `Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - flutter_volume_controller (from `Flutter/ephemeral/.symlinks/plugins/flutter_volume_controller/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_native_event_loop (from `Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  audio_service:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_service/darwin
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  dynamic_color:
    :path: Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  flutter_volume_controller:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_volume_controller/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_native_event_loop:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos

SPEC CHECKSUMS:
  app_links: 05a6ec2341985eb05e9f97dc63f5837c39895c3f
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: eaca2512cf2b39212d724f35d11f46180ad3a33e
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  device_info_plus: 4fb280989f669696856f8b129e4a5e3cd6c48f76
  dynamic_color: cb7c2a300ee67ed3bd96c3e852df3af0300bf610
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  flutter_volume_controller: ae08bf0838e4901f299781c3ab5056dfab0c2f74
  FlutterMacOS: d0db08ddef1a9af05a5ec4b724367152bb0500b1
  media_kit_libs_macos_video: 85a23e549b5f480e72cae3e5634b5514bc692f65
  media_kit_native_event_loop: a80d071c835c612fd80173e79390a50ec409f1b1
  media_kit_video: fa6564e3799a0a28bff39442334817088b7ca758
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_brightness_macos: 2a3ee243f8051c340381e8e51bcedced8360f421
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497

PODFILE CHECKSUM: 0d3963a09fc94f580682bd88480486da345dc3f0

COCOAPODS: 1.16.2
